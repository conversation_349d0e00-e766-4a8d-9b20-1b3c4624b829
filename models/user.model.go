package models

import "gitlab.finema.co/finema/csp/csp-api/consts"

type User struct {
	BaseModel
	Email       string          `json:"email" gorm:"column:email;unique"`
	Password    string          `json:"-" gorm:"column:password"`
	DisplayName *string         `json:"display_name" gorm:"column:display_name"`
	Type        consts.UserType `json:"type" gorm:"column:type"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id"`
	UpdatedByID *string `json:"updated_b_id" gorm:"column:updated_by_id"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id"`

	// Relations
	AccessTokens []UserToken `json:"access_tokens,omitempty" gorm:"foreignKey:UserID"`

	// Self-referencing relations
	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`
	DeletedBy *User `json:"deleted_by,omitempty" gorm:"foreignKey:DeletedByID"`

	Projects []Project `json:"projects,omitempty" gorm:"foreignKey:CreatedBy"`
}

func (User) TableName() string {
	return "users"
}
